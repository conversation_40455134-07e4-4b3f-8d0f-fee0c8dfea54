import pandas as pd

from ultralytics import YOLO
import argparse
import torch
import os,sys
from pathlib import Path
import yaml
import json
from ultralytics.utils import  LOGGER

FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative


def parse_args():
    parser = argparse.ArgumentParser(description='Train a detector')
    parser.add_argument('--config', default="ultralytics/cfg/models/11/yolo11.yaml",
                        help='coco yaml yolo11')
    parser.add_argument("--weights", type=str, default="weights/yolo11n.pt")

    # coovally defined
    parser.add_argument("--label-class", type=str, default="cat,",
                        help="label class")  #
    parser.add_argument("--label-class-count", type=int, default=1, help="label class count")
    parser.add_argument("--work-dir", type=str, default="runs/train")

    # self-defined from coovally
    parser.add_argument("--epochs", type=int, default=1, help="total training epochs")
    parser.add_argument("--batch-size", type=int, default=2, help="total batch size for all GPUs")
    parser.add_argument("--learning-rate", type=float, default=0.001, help="")


    parser.add_argument("--data", default="")
    parser.add_argument("--train-data", default="data/images/train")
    parser.add_argument("--val-data", default="data/images/val")
    parser.add_argument("--train-label", default="data/labels/train")
    parser.add_argument("--val-label", default="data/labels/val")
    parser.add_argument("--device", default="cpu", help="cuda device, i.e. 0 or 0,1,2,3 or cpu")#这个要特别结合每个算法自己的识别方式来定义


    args = parser.parse_args()


    return args


def main():

    args = parse_args()
    # rewrite coco yaml
    with open(args.config, 'r', encoding='utf-8') as f:
        result = yaml.load(f.read(), Loader=yaml.FullLoader)

    class_map = json.loads(args.label_class)

    result['path'] = args.data
    result['train'] = args.train_data
    result['val'] = args.val_data
    result['names'] = {int(k):v for k,v in class_map.items()}
    result['nc'] = args.label_class_count

    result['train_label_dir'] =  args.train_label
    result['val_label_dir'] =  args.val_label

    with open(os.path.join(ROOT, 'coco.yaml'), 'w', encoding='utf-8') as f:
        yaml.dump(data=result, stream=f, allow_unicode=True)

    # Load a model
    model = YOLO(args.weights)
    model.nc = args.label_class_count

    # Train the model
    model.train(
        data= os.path.join(ROOT, 'coco.yaml'),  # path to dataset YAML
        epochs=args.epochs,  # number of training epochs
        batch = args.batch_size,
        lr0 = args.learning_rate,
        project = args.work_dir,
        device=args.device,  # device to run on, i.e. device=0 or device=0,1,2,3 or device=cpu
    )

    model.val(data=os.path.join(ROOT, 'coco.yaml'), split='val', batch=1, device=args.device, project=args.work_dir, name='exp',
              half=False)

    df = pd.read_csv(os.path.join(args.work_dir,'train/results.csv'))
    all_metrics = df.columns
    for ele in range(len(df)):
        epoch_metric = [metric + ": " + str(df.loc[ele][metric]) for metric in all_metrics]
        LOGGER.info(f"{epoch_metric}")


if __name__ == '__main__':
    main()