  《模型集成指南》

  使用中文与我进行交流

  我的项目名称是：projectname = "xmodel"；(从我提供的URL地址总提取出来我项目或者模型的实际名称，以此来替换xmodel这个占位符)
  首先阅读：projectname/目录下所有的代码
  然后按照要求以下步骤，严格地、自动地完成以下要求的所有的任务.后期在测试你生成的train.py及Predict.py脚本时候，必须使用真实的数据
  禁止你进行模拟测试train.py及predict.py的可用性！同时，禁止你在train.py和predict.py脚本使用硬编码的方式写脚本，尤其是数据集的路径。
  
  具体执行步骤
  
      第一步：你是否已经仔细阅读本项目下（projectname）的所有代码和文档及config_demo.json.txt,model_specification.rst.txt,predict_demo.py.txt,train_demo.py.txt？

      如果你已经阅读了，然后确认本项目-projectname是不是支持目标识别任务类型，如果支持则继续下面的操作，
      如果不支持，则直接输出本项目支持的AI任务类型并停止操作；
      
      
      第二步：严格按照model_specification.rst.txt（使用这个指南文件文件中规定的变量或宏变量的写法中的一种，可以借鉴示例代码的写法）。
      在本项目的根文件夹下生成train.py及predict.py（借鉴：train.py.txt, predict.py.txt中的写法）。

      你在阅读本项目目录下的全部代码的过程中要搞清楚项目本身的train和predict是怎么实现的，然后在按照下面的要求生成的新的
      train和predict脚本的时候，整体的规则要按照指南进行，即：train和predict脚本定义的内容的结构和函数的命名规则等等要求，
      实现的细节是可以借鉴本项目已有代的train和predict的实现逻辑。
      
      生成这两个文件的时候参照当前目录下的示例代码

      train.py.txt及predict.py.txt（注意、注意、注意：这里只是示例代码，不是本项目的实际代码！）

      下面是将要生成的train.py和predict.py脚本的开头部分，必须要包含以下代码，以保证其它路径被正确地引用：
          FILE = Path(__file__).resolve()
          ROOT = FILE.parents[0]  
          if str(ROOT) not in sys.path:
              sys.path.append(str(ROOT))  # add ROOT to PATH
          ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative path
      
      借鉴或复用train.py.txt对变量label_class的处理方式；
      
      第三步：看一下本项目的根目录文件夹下是否已经有requirements.txt文件了，如果有就直接进行下一步，如果没有则在本项目的根目录文件夹下生成requirements.txt;
      
      第四步：参照config.json.txt这个文件，为本项目生成：coovally_trainer_config.json文件,放在本项目的根目录下

      第五步：使用项目根目录下的testdata文件夹下面的数据集来测试已经集成的算法，如果该文件夹不存在则自动创建并提示：请上传测试数据集到此文件夹内。然后停止执行，等待用户的操作完成。
	  在收到用户的进一步回复：类似已上传数据等，则继续执行下面的内容。
	  
	  第六步：在不存在testdata这个文件夹的情况下同时用户提示已经操作完毕后，执行下面的操作
      首先,首先创建虚拟环境：projectname,然后通过使用生成的train.py（测试的时候epoch设置为3），训练完成后使用predict.py以此来验证这些脚本正确地被集成了
	  （如果数据集的格式不满足模型的要求，则你写一个脚本转换testdata文件夹下标注文件的格式到模型可使用的标注格式）。
	  
	   在测试的过程中再次校验：
	  （1）生成的coovally_trainer_config.json中的trial_metrics这个字典中涉及到的各个key的写法是否和模型训练过程中日志或控制台输出的一样（包括大小写和一些特殊符号的写法）。
	  （2）在真实环境中生成的脚本过程中所有的参数都是通过命令行传递的，所以测试的时候也必须遵守这一点，同时label_class是通过"{"a":dog, "b": cat}"这种字符串格式来传递的。

      第七步：输出一个tutorial.md文档，内容包含（内容要非常详细）：
      （1）对本项目进行一个详细的介绍，包括本项目的各个组成部分、关键技术等等，使得用户能够快速了解和学习这个模型的核心原理、技术特点及创新点；
      （2）本项目适合解决的、具体的应用场景是什么？在应用的过程中应该注意哪些事项？
      （2）解释如何准备数据集，数据集的文件目录结构是什么样的？
      （3）模型训练过程中可配置参数有哪些？怎么进行配置？
      （4）为了提高模型的性能，在训练过程中建议的配置应该是什么样的？给出这样配置的理由！
      （5）会给出具体的建议来改进当前的模型来解决哪些类型的问题？具体的改进建议是什么给出来！


其它注意事项：

    （1）尽量不要采用宏变量的方式来控制脚本中的参数。
    （2）在生成脚本和配置文件的时候，涉及到路径时，应该采用 ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative path 
         已经提供的路径来索引所有的路径，不要自己去创造不存在的路径
    （3）在本项目的根目录下创建weights子文件夹，用于存放预训练权重。
    （4）当完成train.py和predict.py及config的json文件的生成后，你应该自动写一个脚本
        并自动执行该脚本来下载预训练权重到这个新创建的weights子文件夹中，
        下载的预训练权重应该和config配置文件中默认的配置的预训练权重保持一致。
        完成这些工作后你应该自动删除这个下载文件的脚本及下载过程中产生的缓存文件，以保持文件的干净。
		最终只需要保留下载的预训练权重即可。


到此，结束，不要做其它操作！
  

  
  
  
  
  
  
  
  