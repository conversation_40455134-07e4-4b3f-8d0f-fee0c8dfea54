from ultralytics import YOLO
import argparse
import os
import sys
from pathlib import Path


FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv10 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative



def parse_args():
    parser = argparse.ArgumentParser( description='MMYOLO val (and eval) a model')
    parser.add_argument('--config', default='args.yaml',help='val config file path') #
    # coovally defined
    parser.add_argument('--source', default="runs/test/images/IMG_20210716_183123-02.jpeg")
    parser.add_argument('--out-dir', default="runs/test/out")
    parser.add_argument('--confidence', type=float,default=0.5)
    parser.add_argument("--weights", type=str, default= "runs/train/best_coco_bbox_mAP_epoch_1.pth",help="initial weights path")

    parser.add_argument("--device", default="cpu", help="cuda device, i.e. 0 or 0,1,2,3 or cpu")

    args = parser.parse_args()

    return args


def main():
    args = parse_args()

    # 创建 YOLO 模型实例
    model = YOLO(args.weights)

    # 模型预测
    model.predict(source=args.source, save=True, conf=args.confidence, device=args.device, project=args.out_dir)


if __name__ == '__main__':
    main()
