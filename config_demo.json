{"trial_cnt": 1, "epochs": 30, "trial_metrics": ["train/box_loss", "train/cls_loss", "train/dfl_loss", "metrics/precision(B)", "metrics/recall(B)", "metrics/mAP50(B)", "metrics/mAP50-95(B)", "val/box_loss", "val/cls_loss", "val/dfl_loss"], "evaluation_metric": "metrics/mAP50(B)", "duration": 86400, "input_params": {"config": {"recommend_range": "ultralytics/cfg/models/11/yolo11.yaml", "is_search": false, "type": null, "is_display": true}, "weights": {"recommend_range": "weights/yolo11n.pt", "is_search": false, "type": "str", "is_display": true}, "data": {"recommend_range": "", "is_search": false, "type": null, "is_display": false}, "label-class": {"recommend_range": "cat,", "is_search": false, "type": "str", "is_display": false}, "label-class-count": {"recommend_range": "1", "is_search": false, "type": "int", "is_display": false}, "work-dir": {"recommend_range": "runs/train", "is_search": false, "type": "str", "is_display": false}, "batch-size": {"recommend_range": 2, "is_search": false, "type": "int", "is_display": true}, "learning-rate": {"recommend_range": "0.001", "is_search": false, "type": "float", "is_display": true}, "train-data": {"recommend_range": "/images/train/", "is_search": false, "type": null, "is_display": false}, "val-data": {"recommend_range": "/images/val/", "is_search": false, "type": null, "is_display": false}, "train-label": {"recommend_range": "/annotations/train.json", "is_search": false, "type": null, "is_display": false}, "val-label": {"recommend_range": "/annotations/val.json", "is_search": false, "type": null, "is_display": false}, "device": {"recommend_range": "cpu", "is_search": false, "type": null, "is_display": false}}}